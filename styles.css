* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    background-color: #f8f9fa;
    color: #333;
    overflow: hidden;
}

.app-container {
    display: flex;
    height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: 280px;
    background-color: #fff;
    border-right: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    position: relative;
}

.sidebar.collapsed {
    width: 64px;
    display: flex;
    flex-direction: column;
}

.sidebar.collapsed .logo-text,
.sidebar.collapsed .btn-text,
.sidebar.collapsed .chat-text,
.sidebar.collapsed .section-title,
.sidebar.collapsed .footer-text,
.sidebar.collapsed .beta-tag {
    display: none;
}

.sidebar.collapsed .sidebar-header {
    padding: 20px 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    flex-shrink: 0;
}

.sidebar.collapsed .header-row {
    flex-direction: column;
    gap: 20px;
}

.sidebar.collapsed .logo {
    justify-content: center;
    margin-bottom: 0;
}

.sidebar.collapsed .logo i {
    font-size: 28px;
}

.sidebar.collapsed .logo-img {
    width: 32px;
    height: 32px;
}

.sidebar.collapsed .new-chat-btn {
    width: 48px;
    height: 48px;
    padding: 0;
    justify-content: center;
    border-radius: 10px;
    background: none;
    border: none;
    color: #6b7280;
    margin-bottom: 0;
    font-size: 20px;
}

.sidebar.collapsed .new-chat-btn:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.sidebar.collapsed .chat-history {
    display: none;
}





@keyframes tooltipFadeIn {
    to {
        opacity: 1;
    }
}

.sidebar.collapsed .sidebar-footer {
    padding: 20px 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.sidebar.collapsed .app-info,
.sidebar.collapsed .user-info {
    width: 48px;
    height: 48px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 0;
    color: #6b7280;
    font-size: 20px;
}

.sidebar.collapsed .app-info:hover,
.sidebar.collapsed .user-info:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.sidebar.collapsed .logo {
    justify-content: center;
}

.sidebar.collapsed .sidebar-footer {
    padding: 8px;
}

.sidebar.collapsed .app-info,
.sidebar.collapsed .user-info {
    width: 48px;
    height: 48px;
    border-radius: 10px;
    margin-bottom: 8px;
}

.sidebar.collapsed .sidebar-footer {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 16px 8px;
    margin-top: auto;
}





.sidebar-header {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.header-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 600;
    color: #0ea5e9;
}

.logo i {
    font-size: 20px;
}

.logo-img {
    width: 24px;
    height: 24px;
    object-fit: contain;
}

.new-chat-btn {
    width: 100%;
    padding: 12px 16px;
    background-color: #0ea5e9;
    color: white;
    border: none;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.new-chat-btn:hover {
    background-color: #0284c7;
}



.logo-text,
.btn-text,
.chat-text,
.section-title,
.footer-text {
    transition: all 0.3s ease;
}

.chat-history {
    flex: 1;
    overflow-y: auto;
    padding: 16px 20px;
}

.history-section {
    margin-bottom: 24px;
}

.history-section h3 {
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 8px;
    font-weight: 500;
}

.chat-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    margin-bottom: 4px;
    transition: background-color 0.2s;
    font-size: 14px;
    color: #374151;
}

.chat-item:hover {
    background-color: #f3f4f6;
}

.chat-item.active {
    background-color: #e0f2fe;
    color: #0369a1;
}

.chat-item i {
    font-size: 12px;
    color: #9ca3af;
    flex-shrink: 0;
}

.chat-item span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.sidebar-footer {
    padding: 16px 20px;
}

.app-info, .user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    color: #374151;
    margin-bottom: 4px;
    transition: background-color 0.2s;
}

.app-info:hover, .user-info:hover {
    background-color: #f3f4f6;
}

.beta-tag {
    background-color: #10b981;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: auto;
}

/* User Menu Styles */
.user-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 2000;
    display: none;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.user-menu-overlay.show {
    display: flex;
    opacity: 1;
}

.user-menu {
    position: absolute;
    bottom: 80px;
    left: 20px;
    width: 240px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid #e5e7eb;
    overflow: hidden;
    transform: translateY(10px);
    transition: transform 0.2s ease;
}

.user-menu-overlay.show .user-menu {
    transform: translateY(0);
}

.user-menu-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e5e7eb;
    background-color: #f8f9fa;
}

.user-avatar {
    display: flex;
    justify-content: center;
    margin-bottom: 8px;
}

.user-avatar i {
    font-size: 32px;
    color: #6b7280;
}

.user-email {
    text-align: center;
    font-size: 12px;
    color: #6b7280;
    font-family: monospace;
}

.user-menu-items {
    padding: 8px 0;
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    cursor: pointer;
    font-size: 14px;
    color: #374151;
    transition: background-color 0.2s;
}

.menu-item:hover {
    background-color: #f3f4f6;
}

.menu-item i {
    width: 16px;
    font-size: 14px;
    color: #6b7280;
}

.user-menu-footer {
    border-top: 1px solid #e5e7eb;
    padding: 8px 0;
    background-color: #f8f9fa;
}

.personal-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    cursor: pointer;
    font-size: 14px;
    color: #374151;
    transition: background-color 0.2s;
}

.personal-info:hover {
    background-color: #e5e7eb;
}

.personal-info i {
    width: 16px;
    font-size: 14px;
    color: #6b7280;
}

/* System Settings Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 3000;
    display: none;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal-overlay.show {
    display: flex;
    opacity: 1;
}

.settings-modal {
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.show .settings-modal {
    transform: scale(1);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
    background-color: #f8f9fa;
}

.modal-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin: 0;
}

.close-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #6b7280;
    transition: all 0.2s;
}

.close-btn:hover {
    background-color: #e5e7eb;
    color: #374151;
}

.modal-content {
    height: calc(80vh - 80px);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.settings-tabs {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
    background-color: #f8f9fa;
}

.tab-btn {
    flex: 1;
    padding: 16px 20px;
    border: none;
    background: none;
    font-size: 14px;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s;
    border-bottom: 2px solid transparent;
}

.tab-btn.active {
    color: #0ea5e9;
    border-bottom-color: #0ea5e9;
    background-color: #fff;
}

.tab-btn:hover:not(.active) {
    color: #374151;
    background-color: #e5e7eb;
}

.tab-content {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0;
    border-bottom: 1px solid #f3f4f6;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-label {
    font-size: 14px;
    color: #374151;
    font-weight: 500;
}

.setting-control {
    display: flex;
    align-items: center;
}

.setting-select {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    color: #374151;
    background-color: #fff;
    cursor: pointer;
    min-width: 120px;
}

.setting-select:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Toggle Switch Styles */
.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #d1d5db;
    transition: 0.3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #4f46e5;
}

input:checked + .slider:before {
    transform: translateX(20px);
}

/* Account Management Styles */
.account-info {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 12px;
    margin-bottom: 24px;
}

.account-avatar {
    font-size: 48px;
    color: #6b7280;
}

.account-details h3 {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
    margin: 0 0 8px 0;
}

.account-details p {
    font-size: 14px;
    color: #6b7280;
    margin: 4px 0;
}

.account-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
    margin-bottom: 32px;
}

.action-btn {
    padding: 10px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background-color: #fff;
    color: #374151;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
}

.action-btn:hover {
    border-color: #4f46e5;
    color: #4f46e5;
}

.action-btn.danger {
    border-color: #ef4444;
    color: #ef4444;
}

.action-btn.danger:hover {
    background-color: #ef4444;
    color: white;
}

.usage-stats h4 {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
    margin: 0 0 16px 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
}

.stat-item {
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 4px;
}

.stat-value {
    display: block;
    font-size: 20px;
    font-weight: 600;
    color: #4f46e5;
}

/* Terms and Service Styles */
.terms-content h4 {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin: 0 0 20px 0;
}

.terms-text {
    max-height: 300px;
    overflow-y: auto;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
    line-height: 1.6;
}

.terms-text p {
    margin: 0 0 16px 0;
    font-size: 14px;
    color: #374151;
}

.terms-text strong {
    color: #111827;
    font-weight: 600;
}

.terms-actions {
    display: flex;
    gap: 12px;
}

.terms-actions .action-btn {
    background-color: #4f46e5;
    color: white;
    border-color: #4f46e5;
}

.terms-actions .action-btn:hover {
    background-color: #4338ca;
    border-color: #4338ca;
}

/* Main Content Styles */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    transition: margin-left 0.3s ease;
}

.sidebar.collapsed + .main-content {
    margin-left: 0;
}

.chat-header {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    gap: 16px;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 18px;
    color: #6b7280;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.sidebar-toggle:hover {
    background-color: #f3f4f6;
}

.collapse-btn {
    width: 32px;
    height: 32px;
    padding: 0;
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
}

.collapse-btn:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.collapse-btn i {
    transition: transform 0.3s ease;
}

.sidebar.collapsed .collapse-btn {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    border: none;
    background: none;
    font-size: 18px;
}

.sidebar.collapsed .collapse-btn i {
    transform: rotate(180deg);
}

.model-selector {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    color: #374151;
    transition: border-color 0.2s;
}

.model-selector:hover {
    border-color: #0ea5e9;
}

.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    display: flex;
    flex-direction: column;
}

.welcome-section {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
    padding: 40px 0;
}

.welcome-icon {
    margin-bottom: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.welcome-icon i {
    font-size: 48px;
    color: #0ea5e9;
}

.welcome-logo {
    width: 64px;
    height: 64px;
    object-fit: contain;
}

.welcome-section h1 {
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #111827;
}

.welcome-section p {
    font-size: 16px;
    color: #6b7280;
    margin-bottom: 32px;
    line-height: 1.6;
}

.suggestion-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 16px;
    margin-top: 32px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.suggestion-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 20px 16px;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s;
    background-color: #fff;
}

.suggestion-card:hover {
    border-color: #0ea5e9;
    box-shadow: 0 4px 12px rgba(14, 165, 233, 0.1);
    transform: translateY(-2px);
}

.suggestion-card i {
    font-size: 24px;
    color: #0ea5e9;
}

.suggestion-card span {
    font-size: 14px;
    color: #374151;
    font-weight: 500;
}

.messages {
    flex: 1;
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
}

.message {
    margin-bottom: 24px;
    display: flex;
    gap: 12px;
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background-color: #4f46e5;
    color: white;
}

.message.assistant .message-avatar {
    background-color: #f3f4f6;
    color: #6b7280;
}

.message-content {
    flex: 1;
    max-width: calc(100% - 44px);
}

.message.user .message-content {
    text-align: right;
}

.message-bubble {
    padding: 12px 16px;
    border-radius: 12px;
    font-size: 14px;
    line-height: 1.5;
    word-wrap: break-word;
}

.message.user .message-bubble {
    background-color: #4f46e5;
    color: white;
    margin-left: auto;
    max-width: fit-content;
}

.message.assistant .message-bubble {
    background-color: #f8f9fa;
    color: #374151;
}

/* Input Container Styles */
.input-container {
    padding: 24px;
    background-color: #fff;
}

.input-wrapper {
    max-width: 800px;
    margin: 0 auto;
}

/* Feature Buttons Styles */
.feature-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.feature-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 16px;
    background-color: #fff;
    color: #6b7280;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
    white-space: nowrap;
}

.feature-btn:hover {
    border-color: #d1d5db;
    background-color: #f9fafb;
}

.feature-btn.active {
    border-color: #0ea5e9;
    background-color: #fff;
    color: #0ea5e9;
}

.feature-btn i {
    font-size: 11px;
}

.input-box {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 24px;
    background-color: transparent;
    transition: border-color 0.2s;
}

.input-row {
    display: flex;
    align-items: flex-end;
}

.bottom-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 12px;
    border-top: 1px solid #e5e7eb;
}

.left-buttons {
    display: flex;
    align-items: center;
    gap: 12px;
}

.input-box:focus-within {
    border-color: #d1d5db;
    box-shadow: none;
}

#messageInput {
    flex: 1;
    border: none;
    outline: none;
    resize: none;
    font-size: 14px;
    line-height: 1.5;
    max-height: 120px;
    min-height: 20px;
    font-family: inherit;
}

#messageInput::placeholder {
    color: #9ca3af;
}

.input-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.attach-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s;
    background-color: transparent;
    color: #6b7280;
}

.send-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s;
    background-color: #0ea5e9;
    color: white;
}

.attach-btn:hover {
    background-color: #f3f4f6;
}

.send-btn:hover {
    background-color: #0284c7;
}

.send-btn:disabled {
    background-color: #d1d5db;
    cursor: not-allowed;
}

.input-footer {
    text-align: center;
    margin-top: 12px;
    font-size: 12px;
    color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: 0;
        top: 0;
        height: 100vh;
        z-index: 1000;
        transform: translateX(-100%);
        width: 280px !important;
    }

    .sidebar.collapsed {
        width: 280px !important;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .sidebar.collapsed.open {
        transform: translateX(0);
    }

    .main-content {
        width: 100%;
    }

    .collapse-btn {
        display: none;
    }

    .suggestion-cards {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(3, 1fr);
        gap: 12px;
        max-width: 400px;
    }

    .suggestion-card {
        padding: 16px 12px;
    }
    
    .suggestion-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .welcome-section h1 {
        font-size: 24px;
    }
    
    .input-container {
        padding: 16px;
    }

    .chat-container {
        padding: 16px;
    }

    .feature-buttons {
        gap: 6px;
    }

    .feature-btn {
        padding: 4px 8px;
        font-size: 11px;
    }

    .feature-btn i {
        font-size: 10px;
    }

    .bottom-row {
        padding-top: 8px;
    }

    .user-menu {
        left: 50%;
        transform: translateX(-50%) translateY(10px);
        width: 280px;
    }

    .user-menu-overlay.show .user-menu {
        transform: translateX(-50%) translateY(0);
    }

    .settings-modal {
        width: 95%;
        max-height: 90vh;
    }

    .modal-content {
        height: calc(90vh - 80px);
    }

    .settings-tabs {
        flex-direction: column;
    }

    .tab-btn {
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
        border-right: none;
    }

    .tab-btn.active {
        border-bottom-color: #e5e7eb;
        border-left: 3px solid #4f46e5;
    }

    .account-actions {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .terms-actions {
        flex-direction: column;
    }
}

/* Theme Styles */
.dark-theme {
    background-color: #1f2937;
    color: #f9fafb;
}

.dark-theme .sidebar,
.dark-theme .main-content,
.dark-theme .settings-modal {
    background-color: #374151;
    color: #f9fafb;
}

.dark-theme .sidebar-header,
.dark-theme .modal-header,
.dark-theme .settings-tabs {
    background-color: #4b5563;
    border-color: #6b7280;
}

.dark-theme .chat-item:hover,
.dark-theme .app-info:hover,
.dark-theme .user-info:hover {
    background-color: #4b5563;
}

.dark-theme .chat-item.active {
    background-color: #3730a3;
    color: #a5b4fc;
}

.dark-theme .input-box {
    background-color: #4b5563;
    border-color: #6b7280;
    color: #f9fafb;
}

.dark-theme .message.assistant .message-bubble {
    background-color: #4b5563;
    color: #f9fafb;
}

.dark-theme .suggestion-card {
    background-color: #4b5563;
    border-color: #6b7280;
    color: #f9fafb;
}

.dark-theme .setting-select {
    background-color: #4b5563;
    border-color: #6b7280;
    color: #f9fafb;
}

.dark-theme .account-info,
.dark-theme .stat-item,
.dark-theme .terms-text {
    background-color: #4b5563;
}

/* Font Size Styles */
.font-small {
    font-size: 13px;
}

.font-small .welcome-section h1 {
    font-size: 28px;
}

.font-small .message-bubble {
    font-size: 13px;
}

.font-medium {
    font-size: 14px;
}

.font-large {
    font-size: 16px;
}

.font-large .welcome-section h1 {
    font-size: 36px;
}

.font-large .message-bubble {
    font-size: 16px;
}

/* Smooth transitions for theme changes */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Scrollbar Styles */
.chat-history::-webkit-scrollbar,
.chat-container::-webkit-scrollbar {
    width: 6px;
}

.chat-history::-webkit-scrollbar-track,
.chat-container::-webkit-scrollbar-track {
    background: transparent;
}

.chat-history::-webkit-scrollbar-thumb,
.chat-container::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

.chat-history::-webkit-scrollbar-thumb:hover,
.chat-container::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Animation for typing indicator */
.typing-indicator {
    display: flex;
    gap: 4px;
    padding: 12px 16px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #9ca3af;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}
